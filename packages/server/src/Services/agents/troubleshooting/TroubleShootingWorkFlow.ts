import { CompiledStateGraph, END, MemorySaver, START, StateGraph } from '@langchain/langgraph';
import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import { agentState, intentRouter, IStepInfo, firstParallelStepRouter, createBaseState, consultRouter, IChatParams } from '../../Decls/TroubleShootingFlow';
import { IntentMatcher } from './IntentMatcher';
import { StepProcessor } from './StepProcessor';
import { NODE_NAMES, ROUTER_DECISION, STATUS } from './constants';
import { QuestionCategoryDetector } from './QuestionCategoryDetector';
import { EmotionDetector } from './EmotionDetector';
import { AgentController } from './AgentController';
import { CategoryDetector } from './CategoryDetector';
import { ConsultProblemSolver } from './ConsultProblemSolver';
import { DispatcherAgent } from './DispatcherAgent';
import { GuideAgent } from './GuideAgent';
import { ResolvedAgent } from './ResolvedAgent';

/**
 * Agent 响应接口
 */
export interface IAgentResponse {
    response: string;
    conditions?: IStepInfo;
    threadId: string;
}

/**
 * 故障排除工作流
 * 主要协调各组件完成整体工作流程
 */
@DI.Singleton()
export class TroubleShootingWorkFlow {

    private readonly _logger = LOG.useLogger('TroubleShootingWorkFlow');

    private readonly _productCategoryDetector = DI.use(CategoryDetector);

    private readonly _questionCategoryDetector = DI.use(QuestionCategoryDetector);

    private readonly _intentMatcher = DI.use(IntentMatcher);

    private readonly _stepProcessor = DI.use(StepProcessor);

    private readonly _consultProblemSolver = DI.use(ConsultProblemSolver);

    private readonly _emotionDetector = DI.use(EmotionDetector);

    private readonly _agentController = DI.use(AgentController);

    private readonly _dispatcherAgent = DI.use(DispatcherAgent);

    private readonly _guideAgent = DI.use(GuideAgent);

    private readonly _resolvedAgent = DI.use(ResolvedAgent);

    private _workflow!: CompiledStateGraph<
        typeof agentState.State,
        Record<string, any>,
        '__start__' | 'FirstParallelStep' | 'ConsultProblemSolver' | 'IntentSearchAgent' | 'ProcessStep' | 'UserInput' | 'PassToAgent' | 'DispatcherAgent' | 'GuideAgent' | 'ResolvedAgent'
    >;

    /**
   * 初始化工作流
   */
    @DI.Initializer()
    protected _init(): void {
        this._workflow = this.buildWorkFlow();
        this._logger.info({
            action: '_init',
            message: '故障排除工作流初始化完成'
        });
    }

    /**
   * 处理用户消息
   * @param params 聊天参数
   * @returns Agent响应
   */
    public async chat(
        params: IChatParams
    ): Promise<IAgentResponse> {
        const { userMessage, conversationId, isNewConversation, conditions, inputType } = params;
        try {
            this._logger.info({
                action: 'chat',
                message: '接收用户消息',
                data: {
                    userMessage,
                    threadId: conversationId,
                    conditions,
                    inputType
                }
            });

            // 处理新会话
            if (isNewConversation) {
                return await this._handleNewConversation(params);
            }

            // 处理现有会话
            return await this._handleExistingConversation(params);
        }
        catch (error) {
            this._logger.error({
                action: 'chat',
                message: '处理用户消息失败',
                data: { error, userMessage, conversationId }
            });
            return {
                response: '抱歉，处理您的请求时出现了错误。请稍后再试。',
                threadId: conversationId
            };
        }
    }

    /**
   * 处理新会话
   * @param params 聊天参数
   * @returns Agent响应
   */
    private async _handleNewConversation(
        params: IChatParams
    ): Promise<IAgentResponse> {
        const { userMessage, appId, conversationId, inputType } = params;

        const config = {
            configurable: {
                thread_id: conversationId
            }
        };

        await this._workflow.updateState(config, {
            conversationId: config.configurable.thread_id,
            appId,
            inputType
        });

        const result = await this._workflow.invoke({
            messages: [{ role: 'user', content: userMessage }]
        }, config);

        return {
            response: result.response,
            conditions: result.conditions,
            threadId: config.configurable.thread_id
        };
    }

    /**
   * 处理现有会话
   * @param params 聊天参数
   * @returns Agent响应
   */
    private async _handleExistingConversation(
        params: IChatParams
    ): Promise<IAgentResponse> {
        const { userMessage, conditions, appId, conversationId, inputType } = params;

        const config = {
            configurable: {
                thread_id: conversationId
            }
        };
        const state = await this._workflow.getState(config);

        // 处理条件值
        let doneSteps: string[] = state.values['doneSteps'] ?? [];
        if (conditions && conditions.stepId !== 'category') {
            if (conditions.done && conditions.stepId) {
                // 合并新旧doneSteps
                doneSteps = [...new Set([...doneSteps, conditions.stepId])];
            }
        }

        const currentNode = state.values['currentNode'] ?? '';

        // 构建用户消息
        const userContent = userMessage.length ? userMessage : conditions?.text ?? '';
        const userMessages = userContent ? [{ role: 'user', content: userContent }] : [];

        await this._workflow.updateState(config, {
            appId,
            conversationId: config.configurable.thread_id,
            messages: userMessages,
            doneSteps,
            chooseCondition: conditions ?? {},
            productCategory: conditions?.stepId === 'category' ? conditions.selectOption : state.values['productCategory'],
            inputType
        }, (!userMessage.length && currentNode !== '') ? currentNode : 'UserInput');

        const result = await this._workflow.invoke(null, config);

        // 每次回复后，清空response
        await this._workflow.updateState(config, {
            response: ''
        });

        return {
            response: result.response,
            conditions: result.conditions,
            threadId: config.configurable.thread_id
        };
    }

    /**
   * 构建工作流
   * @returns 已编译的状态图
   */
    public buildWorkFlow(): CompiledStateGraph<
        typeof agentState.State,
        Record<string, any>,
        '__start__' | 'FirstParallelStep' | 'ConsultProblemSolver' | 'IntentSearchAgent' | 'ProcessStep' | 'UserInput' | 'PassToAgent' | 'DispatcherAgent' | 'GuideAgent' | 'ResolvedAgent'
    > {
        const memory = new MemorySaver();
        // 定义工作流图
        const workflow = new StateGraph(agentState)
            .addNode(NODE_NAMES.FIRST_PARALLEL_STEP, this._firstParallelStep.bind(this))
            .addNode(NODE_NAMES.CONSULT_PROBLEM_SOLVER, this._getAggregationPageResponse.bind(this))
            .addNode(NODE_NAMES.INTENT_SEARCH, this._searchIntent.bind(this))
            .addNode(NODE_NAMES.PROCESS_STEP, this._processStep.bind(this))
            .addNode(NODE_NAMES.USER_INPUT, this._userInput.bind(this))
            .addNode(NODE_NAMES.DISPATCHER, this._dispatchAgent.bind(this))
            .addNode(NODE_NAMES.GUIDE_AGENT, this._guideUser.bind(this))
            .addNode(NODE_NAMES.PASS_TO_AGENT, this._passToAgent.bind(this))
            .addNode(NODE_NAMES.RESOLVED_AGENT, this._resolveIssue.bind(this))
            .addEdge(START, NODE_NAMES.FIRST_PARALLEL_STEP)
            .addConditionalEdges(NODE_NAMES.FIRST_PARALLEL_STEP, firstParallelStepRouter, {
                [ROUTER_DECISION.PICK]: NODE_NAMES.USER_INPUT,
                [ROUTER_DECISION.AGENT]: NODE_NAMES.PASS_TO_AGENT,
                [ROUTER_DECISION.CONSULT]: NODE_NAMES.CONSULT_PROBLEM_SOLVER,
                [ROUTER_DECISION.CONTINUE]: NODE_NAMES.INTENT_SEARCH,
            })
            //对接咨询类问题
            .addConditionalEdges(NODE_NAMES.CONSULT_PROBLEM_SOLVER, consultRouter, {
                [ROUTER_DECISION.CONTINUE]: NODE_NAMES.INTENT_SEARCH,
                [ROUTER_DECISION.END]: NODE_NAMES.USER_INPUT
            })
            .addConditionalEdges(NODE_NAMES.INTENT_SEARCH, intentRouter, {
                [ROUTER_DECISION.CONTINUE]: NODE_NAMES.PROCESS_STEP,
                [ROUTER_DECISION.END]: NODE_NAMES.USER_INPUT,
                [ROUTER_DECISION.AGENT]: NODE_NAMES.PASS_TO_AGENT
            })
            .addConditionalEdges(NODE_NAMES.PROCESS_STEP, this._customRouter.bind(this), {
                [ROUTER_DECISION.CONTINUE]: NODE_NAMES.PASS_TO_AGENT,
                [ROUTER_DECISION.END]: NODE_NAMES.USER_INPUT,
                [ROUTER_DECISION.INTENT]: NODE_NAMES.INTENT_SEARCH,
            })
            .addConditionalEdges(NODE_NAMES.USER_INPUT,
                this._userInputRouter.bind(this),
                {
                    [ROUTER_DECISION.PROCESS_STEP]: NODE_NAMES.PROCESS_STEP,
                    [ROUTER_DECISION.CONTINUE]: NODE_NAMES.DISPATCHER,
                }
            )
            .addConditionalEdges(NODE_NAMES.DISPATCHER, (state) => state.routingDecision ?? ROUTER_DECISION.INTENT, {
                [ROUTER_DECISION.INTENT]: NODE_NAMES.INTENT_SEARCH,
                [ROUTER_DECISION.PROCESS_STEP]: NODE_NAMES.PROCESS_STEP,
                [ROUTER_DECISION.CONSULT]: NODE_NAMES.CONSULT_PROBLEM_SOLVER,
                [ROUTER_DECISION.AGENT]: NODE_NAMES.PASS_TO_AGENT,
                [ROUTER_DECISION.END]: NODE_NAMES.GUIDE_AGENT,
                [ROUTER_DECISION.RESOLVED]: NODE_NAMES.RESOLVED_AGENT
            })
            .addEdge(NODE_NAMES.GUIDE_AGENT, NODE_NAMES.USER_INPUT)
            .addEdge(NODE_NAMES.RESOLVED_AGENT, NODE_NAMES.USER_INPUT)
            .addConditionalEdges(NODE_NAMES.PASS_TO_AGENT, (state) => state.response ? 'hasResponse' : 'noResponse', {
                'hasResponse': NODE_NAMES.USER_INPUT,
                'noResponse': END
            });
        // 编译工作流
        return workflow.compile({
            checkpointer: memory,
            interruptBefore: [NODE_NAMES.USER_INPUT]
        }) as CompiledStateGraph<
            typeof agentState.State,
            Record<string, any>,
            '__start__' | 'FirstParallelStep' | 'ConsultProblemSolver' | 'IntentSearchAgent' | 'ProcessStep' | 'UserInput' | 'PassToAgent' | 'DispatcherAgent' | 'GuideAgent' | 'ResolvedAgent'
        >;
    }

    /**
   * 自定义路由函数
   * @param state 状态
   * @returns 决策
   */
    private _customRouter(state: Record<string, any>): string {
        if (state.status === STATUS.PARTIAL_MATCH) {
            return ROUTER_DECISION.END;
        }
        if (state.status === STATUS.COMPLETE_MATCH) {
            return ROUTER_DECISION.CONTINUE;
        }
        if (!state.intent) {
            return ROUTER_DECISION.INTENT;
        }
        if (state.status === STATUS.NO_MATCH) {
            return ROUTER_DECISION.END;
        }
        return ROUTER_DECISION.CONTINUE;
    }

    /**
     * 并行步骤 (检测情绪、问题分类、产品分类)
     * @param state 当前状态
     * @returns 更新后的状态
     */
    private async _firstParallelStep(state: typeof agentState.State): Promise<Partial<typeof agentState.State>> {
        const [emotion, questionCategory, productCategoryState ] = await Promise.all([
            this._emotionDetector.detectEmotion(state),
            this._questionCategoryDetector.detectQuestionCategory(state),
            this._productCategoryDetector.detect(state),
        ]);

        return {
            ...productCategoryState,
            emotion,
            category: questionCategory
        };
    }

    /**
     * 获取聚合页问题回复
     */
    private async _getAggregationPageResponse(
        state: typeof agentState.State
    ): Promise<Partial<typeof agentState.State>> {
        const response = await this._consultProblemSolver.getAggregationPageResponse(state);

        return {
            ...createBaseState(state),
            conditions: {},
            response
        };
    }

    /**
   * 搜索意图
   * @param state 当前状态
   * @returns 更新后的状态
   */
    private async _searchIntent(state: typeof agentState.State): Promise<Partial<typeof agentState.State>> {
        return this._intentMatcher.matchIntent(state);
    }

    /**
   * 处理步骤
   * @param state 当前状态
   * @returns 更新后的状态
   */
    private async _processStep(state: typeof agentState.State): Promise<Partial<typeof agentState.State>> {
        return this._stepProcessor.processStep(state);
    }

    /**
     * 调用 Dispatcher Agent 进行路由决策
     * @param state 当前状态
     * @returns 更新后的状态 (包含路由决策)
     */
    private async _dispatchAgent(state: typeof agentState.State): Promise<Partial<typeof agentState.State>> {
        const routingDecision = await this._dispatcherAgent.dispatch(state);
        return { ...createBaseState(state), routingDecision };
    }

    /**
     * 调用 Guide Agent 进行引导
     * @param state 当前状态
     * @returns 更新后的状态
     */
    private async _guideUser(state: typeof agentState.State): Promise<Partial<typeof agentState.State>> {
        const response = await this._guideAgent.guideUser(state);
        return {
            ...createBaseState(state),
            conditions: {},
            response
        };
    }

    /**
   * 转交人工处理
   * @param state 当前状态
   * @returns 更新后的状态
   */
    private async _passToAgent(state: typeof agentState.State): Promise<Partial<typeof agentState.State>> {
        // 调用AgentController处理所有转人工逻辑
        const response = await this._agentController.handleTransferProcess(
            state.appId,
            state.conversationId,
            state.intent,
            Object.keys(state.conditions).length === 0 && state.intent != ''
        );

        // 如果response为空字符串，说明这是最后一次转人工请求，应该结束会话
        if (!response) {
            this._logger.info({
                action: '_passToAgent',
                message: '转人工处理完成，结束会话',
                data: { conversationId: state.conversationId }
            });

            return {
                ...createBaseState(state),
                conditions: {},
                response: ''
            };
        }

        return {
            ...createBaseState(state),
            // 清空conditions
            conditions: {},
            response
        };
    }

    /**
   * 处理用户输入（此节点现在仅用作中断点）
   * @param state 当前状态
   * @returns 更新后的状态
   */
    private _userInput(state: typeof agentState.State): Partial<typeof agentState.State> {
        this._logger.info({
            action: '_userInput',
            message: '用户输入处理（中断点）',
            data: { state }
        });

        return createBaseState(state);
    }

    /**
     * 用户输入节点的路由逻辑
     * @param state 当前状态
     * @returns 路由决策
     */
    private _userInputRouter(state: typeof agentState.State): string {
        if (state.inputType === 'reply') {
            return ROUTER_DECISION.PROCESS_STEP;
        }
        return ROUTER_DECISION.CONTINUE;
    }

    /**
     * 调用 ResolvedAgent 处理已解决问题
     * @param state 当前状态
     * @returns 更新后的状态
     */
    private async _resolveIssue(state: typeof agentState.State): Promise<Partial<typeof agentState.State>> {
        return this._resolvedAgent.resolveIssue(state);
    }
}
