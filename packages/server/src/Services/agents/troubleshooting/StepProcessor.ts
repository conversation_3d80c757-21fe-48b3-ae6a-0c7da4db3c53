import * as DI from '@reolink-fx/di';
import * as ORM from '@reolink-fx/orm';
import * as DAO from '#/DAO';
import * as _ from '@reolink-fx/utils';
import { SopManager } from '../../sop/SopManager';
import { SopStepManager } from '../../sop/SopStepManager';
import { agentState, IStepInfo, createBaseState } from '../../Decls/TroubleShootingFlow';
import * as LOG from '@reolink-fx/log';
import { IViewSop, IViewStep } from '../../Decls/Sop';
import { NODE_NAMES, STATUS } from './constants';
import { TOptions } from '#/DAO/SopSteps/SopSteps.Custom';
import { ConversationHandler } from '../conversation/ConversationHandler';
import { LLMService } from './LLMService';
import { ChatCompletionMessageParam } from 'openai/resources/chat/completions';
import { PromptManager } from '#/Services/PromptManager';

interface ITranslateStepResponse {
    confirmed: string;
    needGuidance: string;
    step: string;
}

interface ITranslateTextOrDescriptionResponse {
    buttonText: string;
    tipText: string;
    description?: string;
}

interface IDetectCustomerStepStatusResponse {
    isDone: boolean;
    needGuidance: boolean;
    toAgent: boolean;
    guidanceMessage?: string | null;
}

/**
 * 步骤处理器
 * 负责处理故障排除的步骤流程
 */
@DI.Singleton()
export class StepProcessor {

    private readonly _sopManager = DI.use(SopManager);

    private readonly _sopStepManager = DI.use(SopStepManager);

    private readonly _promptManager = DI.use(PromptManager);

    private readonly _llmService = DI.use(LLMService);

    private readonly _articleDAO = ORM.useRepository(DAO.Articles.Entity);

    private readonly _logger = LOG.useLogger('StepProcessor');

    private readonly _conversationHandler = DI.use(ConversationHandler);

    /**
   * 处理当前步骤
   * @param state 当前状态
   * @returns 更新后的状态
   */
    public async processStep(state: typeof agentState.State): Promise<typeof agentState.State> {
        try {
            this._logger.info({
                action: 'processStep',
                message: '开始处理步骤',
                data: {
                    intent: state.intent,
                    doneSteps: state.doneSteps,
                    chooseCondition: state.chooseCondition
                }
            });

            // 处理主SOP流程
            const sops = (await this._sopManager.getSopList({ 'intent': state.intent }))[0];
            if (sops.length === 0) {
                return this._createErrorState(state, 'can not find sop');
            }

            if (!state.chooseCondition.stepId && state.conditions.stepId) {
                //需使用ai识别客户信息进行后续处理
                const result = await this._detectCustomerStepStatus(state);

                // 如果LLM生成了具体的指导信息，则调用新方法处理
                if (result.guidanceMessage) {
                    return await this._handleStepGuidanceMessage(result.guidanceMessage, state);
                }

                if (result.isDone) {
                    state.doneSteps.push(state.conditions.stepId);
                    return await this._selectNextStep(state, sops[0], state.doneSteps);
                }
                // 如果需要引导，并且有多个选项，则返回选项
                if (result.needGuidance) {
                    if (state.conditions.actions?.length && state.conditions.actions?.length  > 1) {
                        return await this._handleUserSelectedOption(sops[0], state.conditions, state);
                    }
                    else {
                        state.doneSteps.push(state.conditions.stepId);
                        return await this._selectNextStep(state, sops[0], state.doneSteps);
                    }
                }
            }

            // 检查是否有用户选择的条件需要处理
            if (state.chooseCondition.stepId && !state.chooseCondition.done && state.chooseCondition.stepId !== 'category') {
                if (state.chooseCondition.selectOption === 'continue') {
                    return await this._selectNextStep(state, sops[0], state.doneSteps);
                }
                return await this._handleUserSelectedOption(sops[0], state.chooseCondition, state);
            }

            return await this._selectNextStep(state, sops[0], state.doneSteps);
        }
        catch (error) {
            this._logger.error({
                action: 'processStep',
                message: '处理步骤失败',
                data: { error }
            });
            return this._createErrorState(state, '处理步骤时发生错误');
        }
    }

    private async _detectCustomerStepStatus(
        state: typeof agentState.State
    ): Promise<IDetectCustomerStepStatusResponse> {

        const prompt = await this._promptManager.generateDetectCustomerStepStatusPrompt(
            state.conditions.step ?? '',
            this._llmService.getLastUserMessage(state.messages)
        );

        const response = await this._llmService.createChatCompletion(prompt, state.messages, true);

        const result = _.String.parseJSON<IDetectCustomerStepStatusResponse>(response, {
            onError: (error: unknown) => {
                this._logger.error({
                    action: '_detectCustomerStepStatus',
                    message: 'JSON解析失败',
                    data: { error, content: response }
                });
                return {
                    isDone: false,
                    needGuidance: true,
                    toAgent: false
                };
            }
        });

        return result;
    }

    /**
     * 处理步骤描述
     * @param step 步骤信息
     * @param state 当前状态
     * @param sop SOP信息
     * @returns 更新后的状态
     */
    private async _handleStepDescription(
        step: DAO.SopSteps.Entity,
        state: typeof agentState.State,
        sop: IViewSop
    ): Promise<typeof agentState.State> {

        const translateStepResponse = await this._translateStep(step.options.description ?? '', state.messages);

        const actions = [
            {
                type: 'reply',
                text: `✅ ${translateStepResponse.confirmed}`,
                payload: 'yes',
                metadata: {
                    stepId: step.id
                }
            }];

        const currentStep = {
            stepId: step.id,
            step: translateStepResponse.step,
            actions: actions
        };

        await this._conversationHandler.saveConversationMetricsToCache({
            zdConversationId: state.conversationId,
            sopId: sop.id,
            lastStepId: step.id,
            pushedStep: {
                sopId: sop.id,
                intent: state.intent,
                steps: [{
                    stepId: step.id,
                    step: step.step,
                    isPushTip: true
                }]
            }
        });
        return {
            ...createBaseState(state),
            response: '',
            currentNode: NODE_NAMES.PROCESS_STEP,
            status: STATUS.PARTIAL_MATCH,
            conditions: currentStep
        };
    }

    /**
     * 处理步骤相关文章
     * @param step 步骤信息
     * @param state 当前状态
     * @param sop SOP信息
     * @returns 更新后的状态
     */
    private async _handleStepArticle(
        step: DAO.SopSteps.Entity,
        state: typeof agentState.State,
        sop: IViewSop
    ): Promise<typeof agentState.State> {
        const article = await this._articleDAO.findOne({
            'where': {
                'id': step.options.articleId
            }
        });

        if (article) {
            const translateTextOrDescriptionResponse = await this._translateTextOrDescription(
                '',
                state.messages);
            const actions = [{
                type: 'link',
                text: translateTextOrDescriptionResponse.buttonText,
                uri: article.htmlUrl
            }];
            const currentStep = {
                stepId: step.id,
                step: translateTextOrDescriptionResponse.tipText,
                actions: actions
            };
            await this._conversationHandler.saveConversationMetricsToCache({
                zdConversationId: state.conversationId,
                sopId: sop.id,
                lastStepId: step.id,
                pushedStep: {
                    sopId: sop.id,
                    intent: state.intent,
                    steps: [{
                        stepId: step.id,
                        step: step.step,
                        isPushTip: true
                    }]
                }
            });
            return {
                ...createBaseState(state),
                response: '',
                currentNode: NODE_NAMES.PROCESS_STEP,
                conditions: currentStep,
                status: STATUS.PARTIAL_MATCH
            };
        }

        return {
            ...createBaseState(state),
            response: '',
            currentNode: NODE_NAMES.PROCESS_STEP,
            status: STATUS.PARTIAL_MATCH
        };
    }

    /**
   * 处理用户选择的选项
   * @param condition 用户选择的条件
   * @param state 当前状态
   * @returns 更新后的状态
   */
    private async _handleUserSelectedOption(
        sop: IViewSop,
        condition: IStepInfo,
        state: typeof agentState.State
    ): Promise<typeof agentState.State> {
        try {
            const step = await this._sopStepManager.getStepById(condition.stepId ?? '');

            await this._conversationHandler.saveConversationMetricsToCache(
                {
                    zdConversationId: state.conversationId,
                    sopId: sop.id,
                    lastStepId: step.id,
                    pushedStep:
                        {
                            sopId: sop.id,
                            intent: state.intent,
                            steps: [{
                                stepId: step.id,
                                step: step.step,
                                isPushTip: true
                            }]
                        }

                });

            // 如果有描述，返回描述
            if (step.options.description) {
                return await this._handleStepDescription(step, state, sop);
            }

            // 如果有文章，返回文章链接
            if (step.options.articleId) {
                return await this._handleStepArticle(step, state, sop);
            }

            return await this._selectNextStep(state, sop, state.doneSteps);
        }
        catch (error) {
            this._logger.error({
                action: '_handleUserSelectedOption',
                message: '处理用户选择选项失败',
                data: { error, condition }
            });
            return this._createErrorState(state, '处理选项时发生错误');
        }
    }

    /**
   * 选择下一个步骤
   * @param state 当前状态
   * @param sop SOP对象
   * @param doneSteps 已完成的步骤
   * @param isSubSOP 是否是子SOP
   * @returns 更新后的状态
   */
    private async _selectNextStep(
        state: typeof agentState.State,
        sop: IViewSop,
        doneSteps: string[]
    ): Promise<typeof agentState.State> {
        // 获取最高优先级的步骤
        const result = this._getHighestPriorityStep(doneSteps, sop);

        if (result.operation) {
            return {
                ...createBaseState(state),
                currentNode: NODE_NAMES.PROCESS_STEP,
                status: STATUS.COMPLETE_MATCH,
                conditions: {},
                doneSteps
            };
        }

        // 如果没有找到任何步骤
        if (!result.step) {
            return this._createErrorState(state, '无法找到合适的步骤/需要转人工');
        }

        const translateStepResponse = await this._translateStep(result.step, state.messages);

        const actions = [
            {
                type: 'reply',
                text: `✅ ${translateStepResponse.confirmed}`,
                payload: 'yes',
                metadata: {
                    stepId: result.stepId
                }
            }];

        await this._conversationHandler.saveConversationMetricsToCache(
            {
                zdConversationId: state.conversationId,
                sopId: sop.id,
                lastStepId: result.stepId,
                pushedStep:
                    {
                        sopId: sop.id,
                        intent: state.intent,
                        steps: [{
                            stepId: result.stepId ?? '',
                            step: result.step ?? '',
                            isPushTip: false
                        }]
                    }

            });

        if (result.options && Object.keys(result.options).length > 0) {
            actions.push({
                type: 'reply',
                text: `❓ ${translateStepResponse.needGuidance}`,
                payload: 'no',
                metadata: {
                    stepId: result.stepId
                }
            });
        }

        const currentStep = {
            stepId: result.stepId,
            step: translateStepResponse.step,
            actions: actions
        };

        return {
            ...createBaseState(state),
            messages: [{ 'role': 'assistant', 'content': translateStepResponse.step }],
            conditions: result.stepId ? currentStep : {},
            status: STATUS.PARTIAL_MATCH,
            currentNode: NODE_NAMES.PROCESS_STEP,
            doneSteps
        };
    }

    /**
   * 获取最高优先级的步骤
   * @param doneSteps 已完成的步骤
   * @param sop SOP对象
   * @returns 最高优先级的步骤信息
   */
    private _getHighestPriorityStep(
        doneSteps: string[],
        sop: IViewSop
    ): {
            stepId?: string;
            step?: string;
            operation?: string;
            options?: TOptions;
            productCategory?: string;
        } {
        let allDone = true;
        let topStep: IViewStep | null = null;
        for (const step of sop.solutions.steps) {
            if (doneSteps.includes(step.stepId)) {
                continue;
            }
            allDone = false;
            if (topStep === null || step.priority > topStep.priority) {
                topStep = step;
            }
        }

        if (allDone) {

            return {
                stepId: topStep?.stepId,
                step: topStep?.step,
                options: topStep?.options,
                operation: 'DONE'
            };
        }

        return {
            stepId: topStep?.stepId,
            step: topStep?.step,
            options: topStep?.options
        };
    }

    private async _translateStep(
        step: string,
        messages: ChatCompletionMessageParam[]
    ): Promise<ITranslateStepResponse> {
        const prompt = await this._promptManager.generateTranslateStepPrompt(
            step,
            this._llmService.getLastUserMessage(messages)
        );
        const response = await this._llmService.createChatCompletion(prompt, [], true);
        return _.String.parseJSON<ITranslateStepResponse>(response, {
            onError: (error: unknown) => {
                this._logger.error({
                    action: 'parseJSON',
                    message: 'JSON解析失败',
                    data: { error, content: response }
                });
                return {
                    confirmed: 'Confirmed',
                    needGuidance: 'Need Guidance',
                    step: ''
                };
            }
        });
    }

    private async _translateTextOrDescription(
        description: string,
        messages: ChatCompletionMessageParam[]
    ): Promise<ITranslateTextOrDescriptionResponse> {
        const prompt = await this._promptManager.generateTranslateTextOrDescriptionPrompt(
            this._llmService.getLastUserMessage(messages),
            description);
        const response = await this._llmService.createChatCompletion(prompt, [], true);
        return _.String.parseJSON<ITranslateTextOrDescriptionResponse>(response, {
            onError: (error: unknown) => {
                this._logger.error({
                    action: '_translateTextOrDescription',
                    message: 'JSON解析失败',
                    data: { error, content: response }
                });
                return {
                    buttonText: '',
                    tipText: '',
                    description: ''
                };
            }
        });
    }

    /**
   * 创建错误状态
   * @param state 当前状态
   * @param errorMessage 错误信息
   * @returns 错误状态
   */
    private _createErrorState(state: typeof agentState.State, errorMessage: string): typeof agentState.State {
        return {
            ...createBaseState(state),
            response: errorMessage,
            status: STATUS.NO_MATCH
        };
    }

    /**
     * 处理由LLM生成的步骤指导信息
     * @param guidanceMessage 指导信息文本
     * @param state 当前状态
     * @returns 更新后的状态，显示指导信息并保持当前步骤
     */
    private async _handleStepGuidanceMessage(
        guidanceMessage: string,
        state: typeof agentState.State
    ): Promise<typeof agentState.State> {

        this._logger.info({
            action: '_handleStepGuidanceMessage',
            message: 'Handling guidance message from LLM',
            data: { guidanceMessage, stepId: state.conditions.stepId }
        });

        const translateStepResponse = await this._translateStep(guidanceMessage, state.messages);

        const actions = [
            {
                type: 'reply',
                text: `✅ ${translateStepResponse.confirmed}`,
                payload: 'yes',
                metadata: {
                    stepId: state.conditions.stepId
                }
            }];

        const currentStep = {
            stepId: state.conditions.stepId,
            step: guidanceMessage,
            actions: actions
        };

        return {
            ...createBaseState(state),
            messages: [{ 'role': 'assistant', 'content': guidanceMessage }],
            response: '', // 清空通用响应，因为主要信息在conditions.step
            currentNode: NODE_NAMES.PROCESS_STEP, // 保持在步骤处理节点
            status: STATUS.PARTIAL_MATCH, // 状态仍为部分匹配，等待用户对指导信息的反馈
            conditions: currentStep,
        };
    }
}
