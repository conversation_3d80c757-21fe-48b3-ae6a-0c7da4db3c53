import * as DI from '@reolink-fx/di';
import * as LOG from '@reolink-fx/log';
import { agentState, createBaseState } from '../../Decls/TroubleShootingFlow';
import { ConversationHandler } from '../conversation/ConversationHandler';
import { TranslationService } from './TranslationService';
import { LLMService } from './LLMService';

/**
 * 解决问题确认Agent
 * 负责处理用户已解决问题的场景，更新会话状态并提供友好的结束响应
 */
@DI.Singleton()
export class ResolvedAgent {
    private readonly _logger = LOG.useLogger('ResolvedAgent');

    private readonly _conversationHandler = DI.use(ConversationHandler);

    private readonly _translationService = DI.use(TranslationService);

    private readonly _llmService = DI.use(LLMService);

    // 固定回复文案
    private readonly _defaultResponse =
        'Thank you for confirming that the issue has been successfully resolved. ' +
        'We appreciate your patience and cooperation throughout the process.' +
        'Should you need any further assistance, please don\'t hesitate to reach out. ' +
        'We\'re always happy to help.';

    /**
     * 处理解决问题确认
     * @param state 当前状态
     * @returns 更新后的状态
     */
    public async resolveIssue(state: typeof agentState.State): Promise<typeof agentState.State> {
        try {
            // 记录问题已解决到会话metrics
            await this._conversationHandler.saveConversationMetricsToCache({
                zdConversationId: state.conversationId,
                issueResolved: 1
            });

            // 获取最后一条用户消息用于语言检测
            const lastUserMessage = this._llmService.getLastUserMessage(state.messages);

            // 翻译固定文案
            const translatedResponse = await this._translationService.translateTexts(
                lastUserMessage,
                [this._defaultResponse]
            );

            this._logger.info({
                action: 'resolveIssue',
                message: '问题已解决，处理完成',
                data: {
                    conversationId: state.conversationId
                }
            });

            // 返回更新后的状态
            return {
                ...createBaseState(state),
                response: translatedResponse[0],
            };
        }
        catch (error) {
            this._logger.error({
                action: 'resolveIssue',
                message: '处理解决问题确认失败',
                data: { error, conversation: state.conversationId }
            });

            // 记录问题已解决到会话metrics（即使出错也尝试记录）
            try {
                await this._conversationHandler.saveConversationMetricsToCache({
                    zdConversationId: state.conversationId,
                    issueResolved: 1
                });
            }
            catch (e) {
                this._logger.error({
                    action: 'resolveIssue',
                    message: '记录问题解决状态失败',
                    data: { error: e, conversation: state.conversationId }
                });
            }

            return {
                ...createBaseState(state),
                response: this._defaultResponse,
            };
        }
    }
}
